-- 课时结算工具数据库表结构
-- 创建数据库
CREATE DATABASE IF NOT EXISTS bright_client DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE bright_client;

-- 教师信息表
CREATE TABLE IF NOT EXISTS teachers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    teacher_id VARCHAR(20) UNIQUE NOT NULL COMMENT '教师工号',
    name VARCHAR(50) NOT NULL COMMENT '教师姓名',
    gender ENUM('M', 'F') NOT NULL COMMENT '性别',
    phone VARCHAR(20) COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    subject VARCHAR(50) COMMENT '任教科目',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
    remark TEXT COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='教师信息表';

-- 学生信息表
CREATE TABLE IF NOT EXISTS students (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id VARCHAR(20) UNIQUE NOT NULL COMMENT '学号',
    name VARCHAR(50) NOT NULL COMMENT '学生姓名',
    gender ENUM('M', 'F') NOT NULL COMMENT '性别',
    grade VARCHAR(10) COMMENT '年级',
    class VARCHAR(20) COMMENT '班级',
    phone VARCHAR(20) COMMENT '学生电话',
    parent_name VARCHAR(50) NOT NULL COMMENT '家长姓名',
    parent_phone VARCHAR(20) NOT NULL COMMENT '家长电话',
    address TEXT COMMENT '家庭地址',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
    remark TEXT COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='学生信息表';

-- 代理信息表
CREATE TABLE IF NOT EXISTS agents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    agent_id VARCHAR(20) UNIQUE NOT NULL COMMENT '代理编号',
    name VARCHAR(50) NOT NULL COMMENT '代理姓名',
    company VARCHAR(100) COMMENT '所属公司',
    phone VARCHAR(20) COMMENT '联系电话',
    email VARCHAR(100) COMMENT '邮箱',
    address TEXT COMMENT '地址',
    commission_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '佣金比例(%)',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
    remark TEXT COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='代理信息表';

-- 课程信息表
CREATE TABLE IF NOT EXISTS courses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_name VARCHAR(100) NOT NULL COMMENT '课程名称',
    subject VARCHAR(50) COMMENT '科目',
    grade VARCHAR(10) COMMENT '适用年级',
    duration INT DEFAULT 60 COMMENT '课程时长(分钟)',
    price DECIMAL(10,2) DEFAULT 0.00 COMMENT '课程单价',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
    remark TEXT COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='课程信息表';

-- 考勤记录表
CREATE TABLE IF NOT EXISTS attendance (
    id INT AUTO_INCREMENT PRIMARY KEY,
    teacher_id INT NOT NULL COMMENT '教师ID',
    student_id INT NOT NULL COMMENT '学生ID',
    course_id INT NOT NULL COMMENT '课程ID',
    agent_id INT COMMENT '代理ID',
    class_date DATE NOT NULL COMMENT '上课日期',
    start_time TIME NOT NULL COMMENT '开始时间',
    end_time TIME NOT NULL COMMENT '结束时间',
    duration INT NOT NULL COMMENT '实际时长(分钟)',
    status ENUM('present', 'absent', 'late', 'leave_early') DEFAULT 'present' COMMENT '出勤状态',
    remark TEXT COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE SET NULL
) COMMENT='考勤记录表';

-- 财务记录表
CREATE TABLE IF NOT EXISTS finance_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    attendance_id INT NOT NULL COMMENT '考勤记录ID',
    teacher_id INT NOT NULL COMMENT '教师ID',
    student_id INT NOT NULL COMMENT '学生ID',
    agent_id INT COMMENT '代理ID',
    course_price DECIMAL(10,2) NOT NULL COMMENT '课程单价',
    teacher_fee DECIMAL(10,2) NOT NULL COMMENT '教师费用',
    agent_commission DECIMAL(10,2) DEFAULT 0.00 COMMENT '代理佣金',
    net_income DECIMAL(10,2) NOT NULL COMMENT '净收入',
    payment_status ENUM('pending', 'paid', 'cancelled') DEFAULT 'pending' COMMENT '支付状态',
    payment_date DATE COMMENT '支付日期',
    remark TEXT COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (attendance_id) REFERENCES attendance(id) ON DELETE CASCADE,
    FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE SET NULL
) COMMENT='财务记录表';

-- 创建索引以提高查询性能
CREATE INDEX idx_teachers_teacher_id ON teachers(teacher_id);
CREATE INDEX idx_students_student_id ON students(student_id);
CREATE INDEX idx_agents_agent_id ON agents(agent_id);
CREATE INDEX idx_attendance_date ON attendance(class_date);
CREATE INDEX idx_attendance_teacher ON attendance(teacher_id);
CREATE INDEX idx_attendance_student ON attendance(student_id);
CREATE INDEX idx_finance_payment_status ON finance_records(payment_status);
CREATE INDEX idx_finance_payment_date ON finance_records(payment_date);

-- 插入一些示例数据（可选）
INSERT INTO teachers (teacher_id, name, gender, phone, email, subject) VALUES
('T001', '张老师', 'F', '13800138001', '<EMAIL>', '数学'),
('T002', '李老师', 'M', '13800138002', '<EMAIL>', '英语'),
('T003', '王老师', 'F', '13800138003', '<EMAIL>', '语文');

INSERT INTO students (student_id, name, gender, grade, class, parent_name, parent_phone) VALUES
('S001', '小明', 'M', '3', '三年级1班', '明爸爸', '13900139001'),
('S002', '小红', 'F', '4', '四年级2班', '红妈妈', '13900139002'),
('S003', '小刚', 'M', '5', '五年级1班', '刚爸爸', '13900139003');

INSERT INTO courses (course_name, subject, grade, duration, price) VALUES
('小学数学基础', '数学', '3', 60, 100.00),
('小学英语口语', '英语', '4', 45, 80.00),
('小学语文阅读', '语文', '5', 60, 90.00);

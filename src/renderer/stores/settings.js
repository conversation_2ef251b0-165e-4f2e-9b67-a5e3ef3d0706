import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'

export const useSettingsStore = defineStore('settings', () => {
  // 数据库设置
  const databaseSettings = reactive({
    host: 'localhost',
    port: 3306,
    user: '',
    password: '',
    database: '',
    ssl: false,
    sslCa: '', // CA证书路径（可选）
    rejectUnauthorized: false // 是否拒绝未授权的连接
  })

  // 应用设置
  const appSettings = reactive({
    theme: 'light',
    language: 'zh-CN',
    autoBackup: true,
    backupInterval: 24 // 小时
  })

  // 连接状态
  const connectionStatus = ref(false)
  const connectionMessage = ref('')

  // 加载设置
  const loadSettings = async () => {
    try {
      const settings = await window.electronAPI.getSettings()
      
      if (settings.database) {
        Object.assign(databaseSettings, settings.database)
      }
      
      if (settings.app) {
        Object.assign(appSettings, settings.app)
      }
    } catch (error) {
      console.error('加载设置失败:', error)
    }
  }

  // 保存设置
  const saveSettings = async () => {
    try {
      const settings = {
        database: { ...databaseSettings },
        app: { ...appSettings }
      }
      
      const result = await window.electronAPI.saveSettings(settings)
      return result
    } catch (error) {
      console.error('保存设置失败:', error)
      return { success: false, message: error.message }
    }
  }

  // 测试数据库连接
  const testConnection = async () => {
    try {
      connectionStatus.value = false
      connectionMessage.value = '正在连接...'
      
      const result = await window.electronAPI.testConnection(databaseSettings)
      
      connectionStatus.value = result.success
      connectionMessage.value = result.message
      
      return result
    } catch (error) {
      connectionStatus.value = false
      connectionMessage.value = error.message
      return { success: false, message: error.message }
    }
  }

  // 重置数据库设置
  const resetDatabaseSettings = () => {
    Object.assign(databaseSettings, {
      host: 'localhost',
      port: 3306,
      user: '',
      password: '',
      database: '',
      ssl: false,
      sslCa: '',
      rejectUnauthorized: false
    })
    connectionStatus.value = false
    connectionMessage.value = ''
  }

  return {
    databaseSettings,
    appSettings,
    connectionStatus,
    connectionMessage,
    loadSettings,
    saveSettings,
    testConnection,
    resetDatabaseSettings
  }
})

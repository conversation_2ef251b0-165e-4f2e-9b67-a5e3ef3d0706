import Layout from '../components/Layout.vue'

const routes = [
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: () => import('../views/Dashboard.vue'),
        meta: { title: '首页', icon: 'House' }
      },
      {
        path: '/teachers',
        name: 'Teachers',
        component: () => import('../views/Teachers.vue'),
        meta: { title: '教师管理', icon: 'User' }
      },
      {
        path: '/students',
        name: 'Students',
        component: () => import('../views/Students.vue'),
        meta: { title: '学生管理', icon: 'UserFilled' }
      },
      {
        path: '/agents',
        name: 'Agents',
        component: () => import('../views/Agents.vue'),
        meta: { title: '代理管理', icon: 'Avatar' }
      },
      {
        path: '/attendance',
        name: 'Attendance',
        component: () => import('../views/Attendance.vue'),
        meta: { title: '考勤统计', icon: 'Calendar' }
      },
      {
        path: '/finance',
        name: 'Finance',
        component: () => import('../views/Finance.vue'),
        meta: { title: '财务统计', icon: 'Money' }
      },
      {
        path: '/settings',
        name: 'Settings',
        component: () => import('../views/Settings.vue'),
        meta: { title: '系统设置', icon: 'Setting' }
      }
    ]
  }
]

export default routes

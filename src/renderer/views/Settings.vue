<template>
  <div class="settings">
    <el-card>
      <template #header>
        <div class="card-header">
          <el-icon><Setting /></el-icon>
          <span>系统设置</span>
        </div>
      </template>

      <el-tabs v-model="activeTab" type="border-card">
        <!-- 数据库设置 -->
        <el-tab-pane label="数据库设置" name="database">
          <el-form
            ref="databaseFormRef"
            :model="settingsStore.databaseSettings"
            :rules="databaseRules"
            label-width="120px"
            class="settings-form"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="主机地址" prop="host">
                  <el-input
                    v-model="settingsStore.databaseSettings.host"
                    placeholder="请输入数据库主机地址"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="端口" prop="port">
                  <el-input-number
                    v-model="settingsStore.databaseSettings.port"
                    :min="1"
                    :max="65535"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="用户名" prop="user">
                  <el-input
                    v-model="settingsStore.databaseSettings.user"
                    placeholder="请输入数据库用户名"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="密码" prop="password">
                  <el-input
                    v-model="settingsStore.databaseSettings.password"
                    type="password"
                    placeholder="请输入数据库密码"
                    show-password
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="数据库名" prop="database">
              <el-input
                v-model="settingsStore.databaseSettings.database"
                placeholder="请输入数据库名称"
                style="width: 300px"
              />
            </el-form-item>

            <!-- SSL设置 -->
            <el-divider content-position="left">SSL 设置</el-divider>
            
            <el-form-item label="启用SSL">
              <el-switch
                v-model="settingsStore.databaseSettings.ssl"
                active-text="启用"
                inactive-text="禁用"
              />
            </el-form-item>

            <template v-if="settingsStore.databaseSettings.ssl">
              <el-form-item label="CA证书">
                <div class="file-input">
                  <el-input
                    v-model="settingsStore.databaseSettings.sslCa"
                    placeholder="请选择CA证书文件（可选，用于验证服务器证书）"
                    readonly
                  />
                  <el-button @click="selectFile('sslCa')" style="margin-left: 10px">
                    选择文件
                  </el-button>
                  <el-button
                    @click="settingsStore.databaseSettings.sslCa = ''"
                    style="margin-left: 5px"
                    type="danger"
                    :disabled="!settingsStore.databaseSettings.sslCa"
                  >
                    清除
                  </el-button>
                </div>
                <div class="form-tip">
                  <el-text type="info" size="small">
                    如果不提供CA证书，将接受所有服务器证书（包括自签名证书）
                  </el-text>
                </div>
              </el-form-item>

              <el-form-item label="证书验证">
                <el-switch
                  v-model="settingsStore.databaseSettings.rejectUnauthorized"
                  active-text="严格验证"
                  inactive-text="允许自签名"
                />
                <div class="form-tip">
                  <el-text type="info" size="small">
                    严格验证：只接受由受信任CA签发的证书；允许自签名：接受自签名证书
                  </el-text>
                </div>
              </el-form-item>
            </template>

            <!-- 连接状态 -->
            <el-form-item label="连接状态">
              <div class="connection-status">
                <el-tag
                  :type="settingsStore.connectionStatus ? 'success' : 'danger'"
                  :icon="settingsStore.connectionStatus ? 'CircleCheck' : 'CircleClose'"
                >
                  {{ settingsStore.connectionMessage || '未测试' }}
                </el-tag>
                <el-button
                  @click="testConnection"
                  :loading="testing"
                  style="margin-left: 10px"
                >
                  测试连接
                </el-button>
              </div>
            </el-form-item>

            <!-- 操作按钮 -->
            <el-form-item>
              <el-button type="primary" @click="saveDatabaseSettings" :loading="saving">
                保存设置
              </el-button>
              <el-button @click="resetDatabaseSettings">
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 应用设置 -->
        <el-tab-pane label="应用设置" name="app">
          <el-form
            :model="settingsStore.appSettings"
            label-width="120px"
            class="settings-form"
          >
            <el-form-item label="主题">
              <el-radio-group v-model="settingsStore.appSettings.theme">
                <el-radio label="light">浅色主题</el-radio>
                <el-radio label="dark">深色主题</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="语言">
              <el-select v-model="settingsStore.appSettings.language" style="width: 200px">
                <el-option label="简体中文" value="zh-CN" />
                <el-option label="English" value="en-US" />
              </el-select>
            </el-form-item>

            <el-form-item label="自动备份">
              <el-switch
                v-model="settingsStore.appSettings.autoBackup"
                active-text="启用"
                inactive-text="禁用"
              />
            </el-form-item>

            <el-form-item
              v-if="settingsStore.appSettings.autoBackup"
              label="备份间隔"
            >
              <el-input-number
                v-model="settingsStore.appSettings.backupInterval"
                :min="1"
                :max="168"
                style="width: 200px"
              />
              <span style="margin-left: 10px">小时</span>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="saveAppSettings" :loading="saving">
                保存设置
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useSettingsStore } from '../stores/settings'

const settingsStore = useSettingsStore()

const activeTab = ref('database')
const testing = ref(false)
const saving = ref(false)
const databaseFormRef = ref()

// 数据库表单验证规则
const databaseRules = {
  host: [
    { required: true, message: '请输入主机地址', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' }
  ],
  user: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ],
  database: [
    { required: true, message: '请输入数据库名', trigger: 'blur' }
  ]
}

// 选择CA证书文件
const selectFile = async (type) => {
  try {
    const result = await window.electronAPI.showOpenDialog({
      title: '选择CA证书文件',
      filters: [
        { name: 'CA证书文件', extensions: ['pem', 'crt', 'cer', 'ca-bundle'] },
        { name: '所有文件', extensions: ['*'] }
      ],
      properties: ['openFile']
    })

    if (!result.canceled && result.filePaths.length > 0) {
      settingsStore.databaseSettings[type] = result.filePaths[0]
      ElMessage.success('CA证书文件选择成功')
    }
  } catch (error) {
    ElMessage.error('选择文件失败: ' + error.message)
  }
}

// 测试数据库连接
const testConnection = async () => {
  if (!databaseFormRef.value) return
  
  try {
    await databaseFormRef.value.validate()
    testing.value = true
    
    const result = await settingsStore.testConnection()
    
    if (result.success) {
      ElMessage.success('数据库连接成功')
    } else {
      ElMessage.error('数据库连接失败: ' + result.message)
    }
  } catch (error) {
    ElMessage.error('请先完善数据库配置信息')
  } finally {
    testing.value = false
  }
}

// 保存数据库设置
const saveDatabaseSettings = async () => {
  if (!databaseFormRef.value) return
  
  try {
    await databaseFormRef.value.validate()
    saving.value = true
    
    const result = await settingsStore.saveSettings()
    
    if (result.success) {
      ElMessage.success('数据库设置保存成功')
    } else {
      ElMessage.error('保存失败: ' + result.message)
    }
  } catch (error) {
    ElMessage.error('请先完善数据库配置信息')
  } finally {
    saving.value = false
  }
}

// 重置数据库设置
const resetDatabaseSettings = () => {
  settingsStore.resetDatabaseSettings()
  ElMessage.info('数据库设置已重置')
}

// 保存应用设置
const saveAppSettings = async () => {
  try {
    saving.value = true
    
    const result = await settingsStore.saveSettings()
    
    if (result.success) {
      ElMessage.success('应用设置保存成功')
    } else {
      ElMessage.error('保存失败: ' + result.message)
    }
  } finally {
    saving.value = false
  }
}
</script>

<style scoped>
.settings {
  max-width: 1000px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: bold;
}

.settings-form {
  padding: 20px;
}

.file-input {
  display: flex;
  align-items: center;
  width: 100%;
}

.file-input .el-input {
  flex: 1;
}

.connection-status {
  display: flex;
  align-items: center;
}

.form-tip {
  margin-top: 5px;
  padding-left: 0;
}
</style>

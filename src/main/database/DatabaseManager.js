const mysql = require('mysql2/promise')
const fs = require('fs')
const path = require('path')

class DatabaseManager {
  constructor() {
    this.connection = null
    this.config = null
  }

  /**
   * 设置数据库连接配置
   * @param {Object} config 数据库配置
   * @param {string} config.host 主机地址
   * @param {number} config.port 端口号
   * @param {string} config.user 用户名
   * @param {string} config.password 密码
   * @param {string} config.database 数据库名
   * @param {boolean} config.ssl 是否启用SSL
   * @param {string} config.sslCa SSL CA证书路径
   * @param {string} config.sslCert SSL客户端证书路径
   * @param {string} config.sslKey SSL客户端密钥路径
   */
  setConfig(config) {
    this.config = {
      host: config.host || 'localhost',
      port: config.port || 3306,
      user: config.user,
      password: config.password,
      database: config.database,
      charset: 'utf8mb4',
      timezone: '+08:00',
      acquireTimeout: 60000,
      timeout: 60000,
      reconnect: true
    }

    // SSL配置
    if (config.ssl) {
      this.config.ssl = {}
      
      if (config.sslCa && fs.existsSync(config.sslCa)) {
        this.config.ssl.ca = fs.readFileSync(config.sslCa)
      }
      
      if (config.sslCert && fs.existsSync(config.sslCert)) {
        this.config.ssl.cert = fs.readFileSync(config.sslCert)
      }
      
      if (config.sslKey && fs.existsSync(config.sslKey)) {
        this.config.ssl.key = fs.readFileSync(config.sslKey)
      }
      
      // 如果没有提供证书文件，使用基本SSL连接
      if (!this.config.ssl.ca && !this.config.ssl.cert && !this.config.ssl.key) {
        this.config.ssl = { rejectUnauthorized: false }
      }
    }
  }

  /**
   * 连接数据库
   */
  async connect() {
    if (!this.config) {
      throw new Error('数据库配置未设置')
    }

    try {
      this.connection = await mysql.createConnection(this.config)
      console.log('数据库连接成功')
      return true
    } catch (error) {
      console.error('数据库连接失败:', error.message)
      throw error
    }
  }

  /**
   * 测试数据库连接
   */
  async testConnection() {
    if (!this.config) {
      throw new Error('数据库配置未设置')
    }

    let testConnection = null
    try {
      testConnection = await mysql.createConnection(this.config)
      await testConnection.ping()
      return { success: true, message: '连接成功' }
    } catch (error) {
      return { success: false, message: error.message }
    } finally {
      if (testConnection) {
        await testConnection.end()
      }
    }
  }

  /**
   * 断开数据库连接
   */
  async disconnect() {
    if (this.connection) {
      await this.connection.end()
      this.connection = null
      console.log('数据库连接已断开')
    }
  }

  /**
   * 执行查询
   * @param {string} sql SQL语句
   * @param {Array} params 参数
   */
  async query(sql, params = []) {
    if (!this.connection) {
      await this.connect()
    }

    try {
      const [rows, fields] = await this.connection.execute(sql, params)
      return { success: true, data: rows, fields }
    } catch (error) {
      console.error('查询执行失败:', error.message)
      return { success: false, error: error.message }
    }
  }

  /**
   * 插入数据
   * @param {string} table 表名
   * @param {Object} data 数据对象
   */
  async insert(table, data) {
    const keys = Object.keys(data)
    const values = Object.values(data)
    const placeholders = keys.map(() => '?').join(', ')
    
    const sql = `INSERT INTO ${table} (${keys.join(', ')}) VALUES (${placeholders})`
    return await this.query(sql, values)
  }

  /**
   * 更新数据
   * @param {string} table 表名
   * @param {Object} data 更新的数据
   * @param {Object} where 条件
   */
  async update(table, data, where) {
    const setClause = Object.keys(data).map(key => `${key} = ?`).join(', ')
    const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ')
    
    const sql = `UPDATE ${table} SET ${setClause} WHERE ${whereClause}`
    const params = [...Object.values(data), ...Object.values(where)]
    
    return await this.query(sql, params)
  }

  /**
   * 删除数据
   * @param {string} table 表名
   * @param {Object} where 条件
   */
  async delete(table, where) {
    const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ')
    const sql = `DELETE FROM ${table} WHERE ${whereClause}`
    
    return await this.query(sql, Object.values(where))
  }

  /**
   * 查询数据
   * @param {string} table 表名
   * @param {Object} where 条件
   * @param {string} orderBy 排序
   * @param {number} limit 限制数量
   */
  async select(table, where = {}, orderBy = '', limit = 0) {
    let sql = `SELECT * FROM ${table}`
    const params = []
    
    if (Object.keys(where).length > 0) {
      const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ')
      sql += ` WHERE ${whereClause}`
      params.push(...Object.values(where))
    }
    
    if (orderBy) {
      sql += ` ORDER BY ${orderBy}`
    }
    
    if (limit > 0) {
      sql += ` LIMIT ${limit}`
    }
    
    return await this.query(sql, params)
  }
}

module.exports = DatabaseManager

const { app, BrowserWindow, ipcMain, dialog } = require('electron')
const path = require('path')
const isDev = process.env.NODE_ENV === 'development'
const DatabaseManager = require('./database/DatabaseManager')
const Store = require('electron-store')

// 初始化数据库管理器和设置存储
const dbManager = new DatabaseManager()
const store = new Store()

// 保持对window对象的全局引用，避免被垃圾回收
let mainWindow

function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 1000,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    titleBarStyle: 'default',
    show: false
  })

  // 加载应用
  if (isDev) {
    mainWindow.loadURL('http://localhost:5173')
    // 开发模式下打开开发者工具
    mainWindow.webContents.openDevTools()
  } else {
    mainWindow.loadFile(path.join(__dirname, '../../dist/index.html'))
  }

  // 窗口准备好后显示
  mainWindow.once('ready-to-show', () => {
    mainWindow.show()
  })

  // 当窗口被关闭时，取消引用window对象
  mainWindow.on('closed', () => {
    mainWindow = null
  })
}

// Electron初始化完成，创建窗口
app.whenReady().then(createWindow)

// 当所有窗口都被关闭时退出应用
app.on('window-all-closed', () => {
  // 在macOS上，应用和菜单栏会保持活跃状态，直到用户明确退出
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('activate', () => {
  // 在macOS上，当点击dock图标并且没有其他窗口打开时，重新创建窗口
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

// IPC 通信处理
ipcMain.handle('get-app-version', () => {
  return app.getVersion()
})

// 文件选择对话框
ipcMain.handle('show-open-dialog', async (event, options) => {
  const result = await dialog.showOpenDialog(mainWindow, options)
  return result
})

// 保存文件对话框
ipcMain.handle('show-save-dialog', async (event, options) => {
  const result = await dialog.showSaveDialog(mainWindow, options)
  return result
})

// 数据库相关IPC处理
ipcMain.handle('test-db-connection', async (event, config) => {
  try {
    dbManager.setConfig(config)
    return await dbManager.testConnection()
  } catch (error) {
    return { success: false, message: error.message }
  }
})

ipcMain.handle('execute-query', async (event, sql, params) => {
  try {
    return await dbManager.query(sql, params)
  } catch (error) {
    return { success: false, error: error.message }
  }
})

// 设置相关IPC处理
ipcMain.handle('get-settings', () => {
  return store.get('settings', {})
})

ipcMain.handle('save-settings', (event, settings) => {
  store.set('settings', settings)

  // 如果保存的是数据库设置，更新数据库管理器配置
  if (settings.database) {
    dbManager.setConfig(settings.database)
  }

  return { success: true }
})

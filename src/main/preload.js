const { contextBridge, ipc<PERSON>enderer } = require('electron')

// 暴露受保护的方法给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 获取应用版本
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  
  // 文件对话框
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  
  // 数据库相关IPC
  testConnection: (config) => ipcRenderer.invoke('test-db-connection', config),
  executeQuery: (query, params) => ipcRenderer.invoke('execute-query', query, params),
  
  // 设置相关
  getSettings: () => ipcRenderer.invoke('get-settings'),
  saveSettings: (settings) => ipcRenderer.invoke('save-settings', settings),
  
  // 通用事件监听
  on: (channel, callback) => {
    ipcRenderer.on(channel, callback)
  },
  
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel)
  }
})

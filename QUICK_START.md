# 快速开始指南

## 1. 环境准备

### 系统要求
- Node.js >= 16.0.0
- MySQL 5.7+ 或 8.0+
- 操作系统：Windows 10+, macOS 10.14+, 或 Linux

### 安装依赖
```bash
npm install
```

## 2. 数据库设置

### 创建数据库
1. 登录到您的 MySQL 服务器
2. 执行 `database/schema.sql` 文件中的SQL语句：
   ```bash
   mysql -u your_username -p < database/schema.sql
   ```

### SSL 连接配置（可选）

#### 场景1：使用自签名证书的测试环境
- 启用SSL：✅
- CA证书：留空
- 证书验证：选择"允许自签名"

#### 场景2：使用正式CA证书的生产环境
- 启用SSL：✅
- CA证书：选择CA证书文件（如：ca-cert.pem）
- 证书验证：选择"严格验证"

#### 场景3：不使用SSL（不推荐生产环境）
- 启用SSL：❌

## 3. 启动应用

### 开发模式
```bash
npm run dev
```

这将启动：
- Vue3 开发服务器（http://localhost:5173）
- Electron 桌面应用

### 生产构建
```bash
npm run build
```

## 4. 首次配置

1. **启动应用**后，会自动打开桌面应用窗口
2. **点击左侧导航栏的"系统设置"**
3. **配置数据库连接**：
   - 主机地址：您的MySQL服务器地址
   - 端口：通常是3306
   - 用户名：MySQL用户名
   - 密码：MySQL密码
   - 数据库名：bright_client
4. **SSL设置**（如果需要）：
   - 根据您的环境选择合适的SSL配置
5. **点击"测试连接"**验证配置
6. **保存设置**

## 5. 功能使用

### 教师管理
- 进入"教师管理"页面
- 点击"添加教师"按钮
- 填写教师信息并保存

### 学生管理
- 进入"学生管理"页面
- 点击"添加学生"按钮
- 填写学生和家长信息并保存

### 其他功能
- 代理管理、考勤统计、财务统计功能框架已就绪
- 可在现有框架基础上继续开发具体业务逻辑

## 6. 开发指南

### 添加新功能
1. 在 `src/renderer/views/` 中创建新页面组件
2. 在 `src/renderer/router/routes.js` 中添加路由
3. 使用 `window.electronAPI.executeQuery()` 进行数据库操作

### 数据库操作示例
```javascript
// 查询教师列表
const result = await window.electronAPI.executeQuery(
  'SELECT * FROM teachers WHERE status = ? ORDER BY created_at DESC',
  ['active']
)

// 添加新教师
const result = await window.electronAPI.executeQuery(
  'INSERT INTO teachers (teacher_id, name, gender, phone, email, subject) VALUES (?, ?, ?, ?, ?, ?)',
  ['T004', '赵老师', 'F', '13800138004', '<EMAIL>', '物理']
)
```

### 状态管理
使用 Pinia 进行状态管理，示例：
```javascript
// 在组件中使用
import { useSettingsStore } from '../stores/settings'
const settingsStore = useSettingsStore()
```

## 7. 常见问题

### Q: 数据库连接失败
A: 检查以下项目：
- MySQL服务是否启动
- 连接信息是否正确
- 防火墙设置
- SSL配置是否匹配服务器设置

### Q: Electron应用无法启动
A: 尝试以下解决方案：
- 确保Node.js版本 >= 16
- 删除node_modules文件夹，重新运行 `npm install`
- 检查是否有端口冲突

### Q: 热重载不工作
A: 确保Vue开发服务器正在运行在 http://localhost:5173

## 8. 项目结构说明

```
src/
├── main/                 # Electron主进程
│   ├── main.js          # 主进程入口
│   ├── preload.js       # 预加载脚本
│   └── database/        # 数据库工具
└── renderer/            # Vue3渲染进程
    ├── views/           # 页面组件
    ├── components/      # 公共组件
    ├── stores/          # 状态管理
    └── router/          # 路由配置
```

## 9. 下一步

1. 根据业务需求完善各功能模块
2. 添加数据验证和错误处理
3. 实现数据导入导出功能
4. 添加报表和统计功能
5. 优化用户体验和界面设计

---

如有问题，请查看 README.md 或提交 Issue。

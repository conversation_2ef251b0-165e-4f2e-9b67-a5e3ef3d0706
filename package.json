{"name": "bright-client", "version": "1.0.0", "description": "课时结算工具 - 基于 Electron + Vue3", "main": "src/main/main.js", "scripts": {"dev": "concurrently \"npm run dev:vue\" \"wait-on http://localhost:5173 && npm run dev:electron\"", "dev:vue": "vite", "dev:electron": "electron .", "build": "npm run build:vue && npm run build:electron", "build:vue": "vite build", "build:electron": "electron-builder", "preview": "vite preview"}, "keywords": ["electron", "vue3", "课时结算", "教育管理"], "author": "Your Name", "license": "MIT", "devDependencies": {"@vitejs/plugin-vue": "^4.4.0", "concurrently": "^8.2.2", "electron": "^27.0.0", "electron-builder": "^24.6.4", "vite": "^4.4.9", "wait-on": "^7.0.1"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.5", "pinia": "^2.1.6", "element-plus": "^2.4.1", "@element-plus/icons-vue": "^2.1.0", "mysql2": "^3.6.3", "electron-store": "^8.1.0"}, "build": {"appId": "com.bright.client", "productName": "课时结算工具", "directories": {"output": "dist"}, "files": ["dist-electron/**/*", "dist/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.education"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}
{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/collapse-transition/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport CollapseTransition from './src/collapse-transition.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElCollapseTransition: SFCWithInstall<typeof CollapseTransition> =\n  withInstall(CollapseTransition)\n\nexport default ElCollapseTransition\n"], "names": [], "mappings": ";;;AAEY,MAAC,oBAAoB,GAAG,WAAW,CAAC,kBAAkB;;;;"}
{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/button/index.ts"], "sourcesContent": ["import { withInstall, with<PERSON><PERSON>Install } from '@element-plus/utils'\nimport Button from './src/button.vue'\nimport ButtonGroup from './src/button-group.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElButton: SFCWithInstall<typeof Button> & {\n  ButtonGroup: typeof ButtonGroup\n} = withInstall(Button, {\n  ButtonGroup,\n})\nexport const ElButtonGroup: SFCWithInstall<typeof ButtonGroup> =\n  withNoopInstall(ButtonGroup)\nexport default ElButton\n\nexport * from './src/button'\nexport * from './src/constants'\nexport type { ButtonInstance, ButtonGroupInstance } from './src/instance'\n"], "names": [], "mappings": ";;;;;;AAGY,MAAC,QAAQ,GAAG,WAAW,CAAC,MAAM,EAAE;AAC5C,EAAE,WAAW;AACb,CAAC,EAAE;AACS,MAAC,aAAa,GAAG,eAAe,CAAC,WAAW;;;;"}
{"version": 3, "file": "collapse2.mjs", "sources": ["../../../../../../packages/components/collapse/src/collapse.vue"], "sourcesContent": ["<template>\n  <div :class=\"rootKls\">\n    <slot />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { collapseEmits, collapseProps } from './collapse'\nimport { useCollapse, useCollapseDOM } from './use-collapse'\n\ndefineOptions({\n  name: 'ElCollapse',\n})\nconst props = defineProps(collapseProps)\nconst emit = defineEmits(collapseEmits)\n\nconst { activeNames, setActiveNames } = useCollapse(props, emit)\n\nconst { rootKls } = useCollapseDOM(props)\n\ndefineExpose({\n  /** @description active names */\n  activeNames,\n  /** @description set active names */\n  setActiveNames,\n})\n</script>\n"], "names": ["_openBlock", "_createElementBlock"], "mappings": ";;;;;mCAUc,CAAA;AAAA,EACZ,IAAM,EAAA,YAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAIA,IAAA,MAAM,EAAE,WAAa,EAAA,cAAA,EAAmB,GAAA,WAAA,CAAY,OAAO,IAAI,CAAA,CAAA;AAE/D,IAAA,MAAM,EAAE,OAAA,EAAY,GAAA,cAAA,CAAe,KAAK,CAAA,CAAA;AAExC,IAAa,MAAA,CAAA;AAAA,MAAA,WAAA;AAAA,MAEX,cAAA;AAAA,KAAA,CAAA,CAAA;AAAA,IAEA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MACD,OAAAA,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;;;;;;;;;;;;"}
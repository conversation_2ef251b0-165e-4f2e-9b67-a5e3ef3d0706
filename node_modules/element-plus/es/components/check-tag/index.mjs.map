{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/check-tag/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport CheckTag from './src/check-tag.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElCheckTag: SFCWithInstall<typeof CheckTag> = withInstall(CheckTag)\nexport default ElCheckTag\n\nexport * from './src/check-tag'\n"], "names": [], "mappings": ";;;;AAEY,MAAC,UAAU,GAAG,WAAW,CAAC,QAAQ;;;;"}
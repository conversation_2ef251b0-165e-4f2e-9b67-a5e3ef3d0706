{"version": 3, "file": "use-checkbox.mjs", "sources": ["../../../../../../../packages/components/checkbox/src/composables/use-checkbox.ts"], "sourcesContent": ["import { computed } from 'vue'\nimport { useFormItem, useFormItemInputId } from '@element-plus/components/form'\nimport { isArray, isPropAbsent } from '@element-plus/utils'\nimport { useDeprecated } from '@element-plus/hooks'\nimport { useCheckboxDisabled } from './use-checkbox-disabled'\nimport { useCheckboxEvent } from './use-checkbox-event'\nimport { useCheckboxModel } from './use-checkbox-model'\nimport { useCheckboxStatus } from './use-checkbox-status'\n\nimport type { ComponentInternalInstance } from 'vue'\nimport type { CheckboxProps } from '../checkbox'\n\nexport const useCheckbox = (\n  props: CheckboxProps,\n  slots: ComponentInternalInstance['slots']\n) => {\n  const { formItem: elFormItem } = useFormItem()\n  const { model, isGroup, isLimitExceeded } = useCheckboxModel(props)\n  const {\n    isFocused,\n    isChecked,\n    checkboxButtonSize,\n    checkboxSize,\n    hasOwnLabel,\n    actualValue,\n  } = useCheckboxStatus(props, slots, { model })\n  const { isDisabled } = useCheckboxDisabled({ model, isChecked })\n  const { inputId, isLabeledByFormItem } = useFormItemInputId(props, {\n    formItemContext: elFormItem,\n    disableIdGeneration: hasOwnLabel,\n    disableIdManagement: isGroup,\n  })\n  const { handleChange, onClickRoot } = useCheckboxEvent(props, {\n    model,\n    isLimitExceeded,\n    hasOwnLabel,\n    isDisabled,\n    isLabeledByFormItem,\n  })\n\n  const setStoreValue = () => {\n    function addToStore() {\n      if (isArray(model.value) && !model.value.includes(actualValue.value)) {\n        model.value.push(actualValue.value)\n      } else {\n        model.value = props.trueValue ?? props.trueLabel ?? true\n      }\n    }\n    props.checked && addToStore()\n  }\n\n  setStoreValue()\n\n  useDeprecated(\n    {\n      from: 'label act as value',\n      replacement: 'value',\n      version: '3.0.0',\n      scope: 'el-checkbox',\n      ref: 'https://element-plus.org/en-US/component/checkbox.html',\n    },\n    computed(() => isGroup.value && isPropAbsent(props.value))\n  )\n\n  useDeprecated(\n    {\n      from: 'true-label',\n      replacement: 'true-value',\n      version: '3.0.0',\n      scope: 'el-checkbox',\n      ref: 'https://element-plus.org/en-US/component/checkbox.html',\n    },\n    computed(() => !!props.trueLabel)\n  )\n\n  useDeprecated(\n    {\n      from: 'false-label',\n      replacement: 'false-value',\n      version: '3.0.0',\n      scope: 'el-checkbox',\n      ref: 'https://element-plus.org/en-US/component/checkbox.html',\n    },\n    computed(() => !!props.falseLabel)\n  )\n\n  return {\n    inputId,\n    isLabeledByFormItem,\n    isChecked,\n    isDisabled,\n    isFocused,\n    checkboxButtonSize,\n    checkboxSize,\n    hasOwnLabel,\n    model,\n    actualValue,\n    handleChange,\n    onClickRoot,\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAQY,MAAC,WAAW,GAAG,CAAC,KAAK,EAAE,KAAK,KAAK;AAC7C,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,WAAW,EAAE,CAAC;AACjD,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC;AACtE,EAAE,MAAM;AACR,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI,kBAAkB;AACtB,IAAI,YAAY;AAChB,IAAI,WAAW;AACf,IAAI,WAAW;AACf,GAAG,GAAG,iBAAiB,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;AACjD,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,mBAAmB,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC;AACnE,EAAE,MAAM,EAAE,OAAO,EAAE,mBAAmB,EAAE,GAAG,kBAAkB,CAAC,KAAK,EAAE;AACrE,IAAI,eAAe,EAAE,UAAU;AAC/B,IAAI,mBAAmB,EAAE,WAAW;AACpC,IAAI,mBAAmB,EAAE,OAAO;AAChC,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,GAAG,gBAAgB,CAAC,KAAK,EAAE;AAChE,IAAI,KAAK;AACT,IAAI,eAAe;AACnB,IAAI,WAAW;AACf,IAAI,UAAU;AACd,IAAI,mBAAmB;AACvB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,aAAa,GAAG,MAAM;AAC9B,IAAI,SAAS,UAAU,GAAG;AAC1B,MAAM,IAAI,EAAE,EAAE,EAAE,CAAC;AACjB,MAAM,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;AAC5E,QAAQ,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC5C,OAAO,MAAM;AACb,QAAQ,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,SAAS,KAAK,IAAI,GAAG,EAAE,GAAG,KAAK,CAAC,SAAS,KAAK,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC;AACvG,OAAO;AACP,KAAK;AACL,IAAI,KAAK,CAAC,OAAO,IAAI,UAAU,EAAE,CAAC;AAClC,GAAG,CAAC;AACJ,EAAE,aAAa,EAAE,CAAC;AAClB,EAAE,aAAa,CAAC;AAChB,IAAI,IAAI,EAAE,oBAAoB;AAC9B,IAAI,WAAW,EAAE,OAAO;AACxB,IAAI,OAAO,EAAE,OAAO;AACpB,IAAI,KAAK,EAAE,aAAa;AACxB,IAAI,GAAG,EAAE,wDAAwD;AACjE,GAAG,EAAE,QAAQ,CAAC,MAAM,OAAO,CAAC,KAAK,IAAI,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACjE,EAAE,aAAa,CAAC;AAChB,IAAI,IAAI,EAAE,YAAY;AACtB,IAAI,WAAW,EAAE,YAAY;AAC7B,IAAI,OAAO,EAAE,OAAO;AACpB,IAAI,KAAK,EAAE,aAAa;AACxB,IAAI,GAAG,EAAE,wDAAwD;AACjE,GAAG,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;AACxC,EAAE,aAAa,CAAC;AAChB,IAAI,IAAI,EAAE,aAAa;AACvB,IAAI,WAAW,EAAE,aAAa;AAC9B,IAAI,OAAO,EAAE,OAAO;AACpB,IAAI,KAAK,EAAE,aAAa;AACxB,IAAI,GAAG,EAAE,wDAAwD;AACjE,GAAG,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;AACzC,EAAE,OAAO;AACT,IAAI,OAAO;AACX,IAAI,mBAAmB;AACvB,IAAI,SAAS;AACb,IAAI,UAAU;AACd,IAAI,SAAS;AACb,IAAI,kBAAkB;AACtB,IAAI,YAAY;AAChB,IAAI,WAAW;AACf,IAAI,KAAK;AACT,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,IAAI,WAAW;AACf,GAAG,CAAC;AACJ;;;;"}
import type { AddedKeywordDefinition, AnySchemaObject, KeywordErrorCxt, KeywordCxtParams } from "../../types";
import type { SchemaCxt, SchemaObjCxt } from "..";
import { SubschemaArgs } from "./subschema";
import { Code, Name, CodeGen } from "../codegen";
import type { JSONType } from "../rules";
import { ErrorPaths } from "../errors";
export declare function validateFunctionCode(it: SchemaCxt): void;
export declare class KeywordCxt implements KeywordErrorCxt {
    readonly gen: CodeGen;
    readonly allErrors?: boolean;
    readonly keyword: string;
    readonly data: Name;
    readonly $data?: string | false;
    schema: any;
    readonly schemaValue: Code | number | boolean;
    readonly schemaCode: Code | number | boolean;
    readonly schemaType: JSONType[];
    readonly parentSchema: AnySchemaObject;
    readonly errsCount?: Name;
    params: KeywordCxtParams;
    readonly it: SchemaObjCxt;
    readonly def: AddedKeywordDefinition;
    constructor(it: SchemaObjCxt, def: AddedKeywordDefinition, keyword: string);
    result(condition: Code, successAction?: () => void, failAction?: () => void): void;
    failResult(condition: Code, successAction?: () => void, failAction?: () => void): void;
    pass(condition: Code, failAction?: () => void): void;
    fail(condition?: Code): void;
    fail$data(condition: Code): void;
    error(append?: boolean, errorParams?: KeywordCxtParams, errorPaths?: ErrorPaths): void;
    private _error;
    $dataError(): void;
    reset(): void;
    ok(cond: Code | boolean): void;
    setParams(obj: KeywordCxtParams, assign?: true): void;
    block$data(valid: Name, codeBlock: () => void, $dataValid?: Code): void;
    check$data(valid?: Name, $dataValid?: Code): void;
    invalid$data(): Code;
    subschema(appl: SubschemaArgs, valid: Name): SchemaCxt;
    mergeEvaluated(schemaCxt: SchemaCxt, toName?: typeof Name): void;
    mergeValidEvaluated(schemaCxt: SchemaCxt, valid: Name): boolean | void;
}
export declare function getData($data: string, { dataLevel, dataNames, dataPathArr }: SchemaCxt): Code | number;
